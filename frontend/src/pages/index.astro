---
import Site from '../layouts/Site.astro'
import Gallery from '../components/container/Gallery.astro'
import Link from '../components/elements/Link.astro'
import FaqItem from '../components/patterns/FaqItem.astro'

import { Var } from '../script/var'
import { currentPage, currentTopic, currentSubTopic } from '../script/store'
currentPage.set('home')
currentTopic.set('')
currentSubTopic.set('')

const title = `${Var.app.name} — ${Var.app.tagline}`
const description = Var.app.description
---

<Site title={title} description={description}>
    <section class="logo-section">
      <div class="logo-container">
        <img
          src="https://res.cloudinary.com/dyygc6dx2/image/upload/v1743453235/logotype_pxbcf5.svg"
          alt="SenseFolks Logo"
          class="sensefolks-logo"
          width="225"
          height="59"
        />
        <p class="footnote logo-footnote">
          Made by <Link href="https://projckt.com" theme="default" target="_blank">Projckt</Link>
        </p>
      </div>
    </section>
    <section class="hero-section spacing__vertical--900">
      <div class="spacing__vertical--900 hero-container">
        <h1>Read your customers' mind</h1>
        <div class="spacing__vertical--50"></div>
        <h2>Micro-surveys that help you <u>make epic product decisions</u></h2>
        <div class="dot-grid hero-dot-separator"></div>
        <!-- <div class="spacing__vertical--200"></div>
        <div class="hero__buttons spacing__vertical--100">
          <Link href="#surveys" variant="button" fill="outline" theme="default">
            <strong>Explore Surveys</strong>
          </Link>
          &nbsp;&nbsp;
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Get Started</strong>
          </Link>
        </div> -->
        <div class="spacing__vertical--400"></div>
        <Gallery>
          <div class="gallery__item">
            <img
              class="benefits__icons"
              src="./target.svg"
              width="56"
              height="56"
              alt="SenseFolks surveys gather feedback about a specific aspect of the product"
            />
            <h3><strong>Super Focused</strong></h3>
            <p>Each survey is designed to gather feedback about a specific aspect of the product</p>
          </div>
          <div class="gallery__item">
            <img
              class="benefits__icons"
              src="./wind.svg"
              width="56"
              height="56"
              alt="SenseFolks surveys use proven research methods for accurate insights"
            />
            <h3><strong>High Accuracy</strong></h3>
            <p>Our surveys are based on proven research methods that yield accurate results</p>
          </div>
          <div class="gallery__item">
            <img
              class="benefits__icons"
              src="./bolt.svg"
              width="56"
              height="56"
              alt="No need for customisations. Embed SenseFolks surveys on your website"
            />
            <h3><strong>Quick & Easy</strong></h3>
            <p>Launch surveys in minutes. Embed it on your website or share it as a link</p>
          </div>
        </Gallery>
      </div>
    </section>
    <section class="spacing__vertical--1200 dashboard-section full-width-section">
      <h1 style="color:#F3E5F5">Know what your customers <em>really want</em>. <span style="color: #E1BEE7">Join the waitist</span></h1>
      <div class="spacing__vertical--200"></div>
      <form class="waitlist-form" id="waitlist-form">
        <div class="form-group">
          <input type="email" placeholder="Enter your email" class="waitlist-input" id="waitlist-email" required>
          <button type="submit" class="waitlist-button">Join Waitlist</button>
        </div>
        <div class="error-message" id="waitlist-error"></div>
      </form>
      <div class="spacing__vertical--200"></div>
      <p style="color:#F3E5F5">* We will notify you when we launch</p>
      <div class="spacing__vertical--400"></div>
    </section>
    <section id="surveys" class="spacing__vertical--1600">
      <article>
        <h1>The Surveys</h1>
        <div class="spacing__vertical--50"></div>
        <h2>Designed with intent and purpose</h2>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--senseprice">SensePrice</h3>
            <div class="spacing__vertical--50"></div>
            <h3>Discover how much your customers are willing to pay</h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  Eliminate guesswork from pricing
                </p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  Confidently set the optimal price
                </p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Don't leave money on the table</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--sensepriority">SensePriority</h3>
            <div class="spacing__vertical--50"></div>
            <h3>
              Prioritize features that drive customer satisfaction and retention
            </h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Identify high-impact features</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Enhance product-market fit</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Improve your ROI</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--sensechoice">SenseChoice</h3>
            <div class="spacing__vertical--50"></div>
            <h3>
              Prioritize features that drive customer satisfaction and retention
            </h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Identify high-impact features</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Enhance product-market fit</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Improve your ROI</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--sensepoll">SensePoll</h3>
            <div class="spacing__vertical--50"></div>
            <h3>Create highly flexible polls to capture opinions, preferences, and feedback</h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Use single or multi-choice questions</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Get clear & structured insights</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Identify trends & optimize your decisions</p>
              </li>
            </ul>
          </div>
        </div>
        <div class="row row__justify--space-between spacing__vertical--900">
          <div class="survey__preview__bg dot-grid"></div>
          <div class="survey__thumbnail__description spacing__vertical--200">
            <h3 class="survey__thumbnail__title survey__thumbnail__title--sensequery">SenseQuery</h3>
            <div class="spacing__vertical--50"></div>
            <h3>Uncover unanswered questions and identify what your audience wants to know</h3>
            <div class="separator spacing__vertical--100"></div>
            <ul class="survey__benefits no-margin--left">
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Spot knowledge or content gaps</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Enhance clarity</p>
              </li>
              <li class="row spacing__vertical--100">
                <img
                  class="material-symbols-outlined"
                  src="./check.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">Eliminate confusion</p>
              </li>
            </ul>
          </div>
        </div>
      </article>
    </section>
    <!-- <section class="spacing__vertical--900">
      <article>
        <div class="cta__container">
          <h2 class="spacing__vertical--100">
            Get product feedback that matters
          </h2>
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Get Started</strong>
          </Link>
        </div>
      </article>
    </section> -->
    <section class="spacing__vertical--1600 dashboard-section full-width-section" style="margin-bottom: 0">
      <article>
        <h1 style="background: linear-gradient(to right, #80DEEA, #E0F7FA); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">The Dashboard</h1>
        <div class="spacing__vertical--50"></div>
        <h2>Everything in one place</h2>
        <div class="spacing__vertical--900">
          <div class="dashboard__thumbnail"></div>
          <div class="dashboard__benefit">
            <ul
              class="no-margin--left row row__justify--space-between row--wrap no-margin--bottom"
            >
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Create & organize surveys</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Get automated insights</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Make smart decisions</strong>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div class="spacing__vertical--900">
          <div class="dashboard__thumbnail"></div>
          <div class="dashboard__benefit">
            <ul
              class="no-margin--left row row__justify--space-between row--wrap no-margin--bottom"
            >
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Get insights by country</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Spot regional preferences</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Optimize regional strategies</strong>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div class="spacing__vertical--900">
          <div class="dashboard__thumbnail"></div>
          <div class="dashboard__benefit no-margin--bottom">
            <ul
              class="no-margin--left row row__justify--space-between row--wrap no-margin--bottom"
            >
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Define your personas</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Get insights by persona</strong>
                </p>
              </li>
              <li class="row spacing__vertical--50">
                <img
                  class="material-symbols-outlined "
                  src="./check--light.svg"
                  width="24"
                  height="32"
                  alt="green check"
                />
                <p class="no-margin--bottom">
                  <strong>Optimize offerings for personas</strong>
                </p>
              </li>
            </ul>
          </div>
        </div>
      </article>
    </section>
    <section class="spacing__vertical--1600 full-width-section" style="background: #121212; text-align: center; color: white; margin-top: 0; padding: 8em 0;">
      <h1 style="color: white">Get deeper customer insights.<br/> <span style="color: rgba(255, 255, 255, 0.6)">Sign up for early access</span></h1>
      <div class="spacing__vertical--200"></div>
      <form class="early-access-form" id="early-access-form">
        <div class="form-group">
          <input type="email" placeholder="Enter your email" class="early-access-input" id="early-access-email" required>
          <button type="submit" class="early-access-button">Get Early Access</button>
        </div>
        <div class="error-message" id="early-access-error"></div>
      </form>
      <div class="spacing__vertical--200"></div>
      <p>* We will send early access invites when we launch</p>
    </section>
    <!-- <section class="spacing__vertical--900">
      <article>
        <div class="cta__container">
          <h2 class="spacing__vertical--100">
            Get automated analysis & insights
          </h2>
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Sign Up</strong>
          </Link>
        </div>
      </article>
    </section> -->
    <section class="spacing__vertical--600">
      <article>
        <h1 class="spacing__vertical--100">FAQs</h1>
        <div class="content--narrow">
          <FaqItem question="What is SenseFolks?">
              <p class="spacing__vertical--50">
                SenseFolks is an all-in-one platform designed to help you
                understand your customers better. We offer a suite of survey
                tools that allow you to gather valuable insights on pricing,
                features, branding, content, and customer engagement. Our goal
                is to make it easy for you to make informed decisions and build
                products that people love.
              </p>
            </FaqItem>
            <div class="spacing__vertical--150"></div>
            <FaqItem question="How is it different from other surveys?">
              <p class="spacing__vertical--50">
                SenseFolks stands out because we offer ready-made surveys
                designed for specific purposes that are based on established
                research methods. Unlike generic survey tools, you don't have to
                spend time customizing forms and figuring out how to analyze the
                results. SenseFolks automatically processes and analyzes the
                data, saving you time and efforts on data formatting and
                calculations, so you can focus on making informed decisions
                right away.
              </p>
            </FaqItem>
            <div class="spacing__vertical--150"></div>
            <FaqItem question="Can it help me make better decisions?">
              <p class="spacing__vertical--50">
                Definitely! The core purpose of SenseFolks is to provide you
                with the insights needed to make informed, data-driven
                decisions. Whether you’re setting a price, choosing new features
                to develop, or refining your brand strategy, our surveys give
                you the information you need to make decisions with confidence.
              </p>
            </FaqItem>
            <div class="spacing__vertical--150"></div>
            <FaqItem question="Who can benefit from SenseFolks?">
              <p class="spacing__vertical--50">
                SenseFolks can benefit businesses of all sizes and industries.
                Whether you’re a startup looking to validate your product idea,
                an established company aiming to improve customer satisfaction
                or an independent author trying to set the price of your
                upcoming book, we can offer valuable insights to help you
                succeed.
              </p>
            </FaqItem>
            <div class="spacing__vertical--150"></div>
            <!-- <span class="footnote">
              <Link href=`${Var.app.website.url}/faq/` theme="default" target="_blank">
              READ MORE FAQs
              </Link>
            </span> -->

        </div>
      </article>
    </section>
    <div class="separator spacing__vertical--300"></div>
    <section class="spacing__vertical--200">
      <article>
        <div class="copyright-section">
          <p class="footnote">© {new Date().getFullYear()}&nbsp;{Var.app.name}. All rights reserved.</p>
          <p class="footnote">Made by <Link href="https://projckt.com" theme="default" target="_blank">Projckt</Link></p>
        </div>
      </article>
    </section>
    <!-- <section class="spacing__vertical--600">
      <article>
        <div class="cta__container">
          <h2 class="spacing__vertical--100">
            Drastically improve your product
          </h2>
          <Link
            href=`${Var.app.url}/signup/`
            variant="button"
            fill="solid"
            theme="default"
          >
            <strong>Get Started</strong>
          </Link>
        </div>
      </article>
    </section> -->
</Site>

<style>
  h1 {
    font-weight: 400;
    font-size: 3.5em;
  }

  body {
    width: 100%;
    max-width: none;
  }

  article {
    width: 870px;
    margin: 0 auto;
  }

  section {
    text-align: center;
    max-width: 1024px;
    margin: 0 auto;
  }

  .full-width-section {
    max-width: none;
    width: 100%;
  }

  .dashboard__benefit {
    text-align: left;
  }

  .dashboard__benefit .row {
    align-items: center;
  }

  .dashboard-section {
    background: linear-gradient(135deg, var(--color__indigo--900) 0%, var(--color__indigo--700) 100%);
    padding: calc(var(--padding) * 4 + 5em) calc(var(--padding) * 4) calc(var(--padding) * 4) calc(var(--padding) * 4);
    position: relative;
  }

  .dashboard-section h1 {
    color: var(--color__white);
  }

  .dashboard-section h2 {
    color: var(--color__indigo--50);
  }

  .dashboard-section p {
    color: var(--color__white);
  }



  .dot-separator {
    height: 100px;
    width: 100%;
  }

  .hero-dot-separator {
    width: 100%;
    aspect-ratio: 16 / 9;
    margin-top: 3em;
  }

  .spacing__vertical--1600 {
    margin: 16em 0;
  }

  .copyright-section {
    text-align: center;
    margin-bottom: 2em;
  }

  .copyright-section p {
    margin: 0.5em 0;
    color: var(--color__grey--500);
  }

  .logo-section {
    display: flex;
    justify-content: center;
    align-items: center;
    /* padding: 4em 0 2em 0;
     */
     margin-top: 4em;
  }

  .logo-container {
    text-align: center;
  }

  .sensefolks-logo {
    max-width: 100%;
    height: auto;
  }

  .logo-footnote {
    margin-bottom: 0;
    text-align: right;
  }

  .hero-section {
    margin-top: 3em;
  }

  .content--narrow {
    width: 560px;
    text-align: left;
    margin: 0 auto;
  }

  .gallery__item {
    width: 310px;
    text-align: center;
    background: var(--color__white);
    padding: calc(var(--padding) * 2) calc(var(--padding) * 1.5);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(63, 81, 181, 0.15);
    border: 1px solid rgba(63, 81, 181, 0.1);
  }

  .cta__container {
    color: var(--color__grey--800);
    border: 1px solid var(--color__grey--800);
    background: var(--color__grey--50);
    padding: var(--padding) var(--padding) calc(3 * var(--padding)) var(--padding);
    border-radius: calc(var(--border-radius) * 2);
  }

  .cta__container h2 {
    color: var(--color__grey--700);
  }

  .survey__benefits .row {
    align-items: center;
  }

  .survey__preview__bg {
    height: 400px;
    width: 50%;

  }

  .survey__thumbnail__description {
    width: 45%;
    text-align: left;
  }

  .survey__thumbnail__description h2 {
    margin-bottom: 0.5em;
  }

  .survey__thumbnail__title {
    display: inline-block;
    border: 1px solid var(--color__indigo--300);
    padding: 0.25em 1em;
    border-radius: var(--border-radius);
    margin-bottom: 0.5em;
  }

  .survey__thumbnail__title--senseprice {
    border-color: #6a1b9a;
    color: #6a1b9a;
    background: #f3e5f5;
  }

  .survey__thumbnail__title--sensechoice {
    border-color: #1565c0;
    color: #1565c0;
    background: #e3f2fd;
  }

  .survey__thumbnail__title--sensepoll {
    border-color: #303f9f;
    color: #303f9f;
    background: #e8eaf6;
  }

  .survey__thumbnail__title--sensequery {
    border-color: #006064;
    color: #006064;
    background: #e0f7f9;
  }

  .survey__thumbnail__title--sensepriority {
    border-color: #00695c;
    color: #00695c;
    background: #e0f2f1;
  }

  .black-cta-section {
    background: #000000;
    padding: 4em 0;
    margin: 0 calc(-50vw + 50%);
    width: 100vw;
  }

  .black-cta-section .cta__container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--padding);
    text-align: center;
  }

  /* Waitlist Form Styles */
  .waitlist-form, .early-access-form {
    max-width: calc(var(--padding) * 31.25); /* 500px equivalent */
    margin: 0 auto;
  }

  .form-group {
    display: flex;
    align-items: stretch;
  }

  .waitlist-input {
    flex: 1;
    padding: var(--padding) calc(var(--padding) * 1.25);
    border: 1px solid white;
    border-radius: calc(var(--border-radius) * 1.6) 0 0 calc(var(--border-radius) * 1.6);
    border-right: none;
    font-size: calc(var(--font-size__body) * 1.06);
    outline: none;
    background: none;
    color: #F3E5F5;
  }

  .early-access-input {
    flex: 1;
    padding: var(--padding) calc(var(--padding) * 1.25);
    border: 1px solid white;
    border-radius: calc(var(--border-radius) * 1.6) 0 0 calc(var(--border-radius) * 1.6);
    border-right: none;
    font-size: calc(var(--font-size__body) * 1.06);
    outline: none;
    background: none;
    color: white;
  }

  .waitlist-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
  }

  .early-access-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
  }

  .waitlist-button {
    padding: var(--padding) calc(var(--padding) * 2);
    border: 1px solid white;
    border-radius: 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0;
    background: white;
    color: var(--color__indigo--700);
    font-size: calc(var(--font-size__body) * 1.06);
    font-weight: 600;
    cursor: pointer;
    white-space: nowrap;
  }

  .early-access-button {
    padding: var(--padding) calc(var(--padding) * 2);
    border: 1px solid white;
    border-radius: 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0;
    background: white;
    color: #121212;
    font-size: calc(var(--font-size__body) * 1.06);
    font-weight: 600;
    cursor: pointer;
    white-space: nowrap;
  }

  .error-message {
    color: var(--color__red--100);
    font-size: calc(var(--font-size__body) * 0.82);
    margin-top: var(--padding);
    text-align: center;
    display: none;
  }

  .spinner {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: var(--padding);
    height: var(--padding);
    animation: spin 1s linear infinite;
    display: inline-block;
    margin: 0 auto;
  }

  .spinner.waitlist-spinner {
    border-top: 2px solid var(--color__indigo--700);
  }

  .spinner.early-access-spinner {
    border-top: 2px solid #121212;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .waitlist-button.loading,
  .early-access-button.loading {
    pointer-events: none;
    opacity: 0.8;
  }

  .early-access-input {
    border-color: white;
    background: none;
    color: white;
  }

  .early-access-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
  }

  .early-access-button {
    border-color: white;
    background: white;
    color: #121212;
  }

  .dashboard__thumbnail {
    display: flex;
    justify-content: space-around;
    align-items: center;
    border: 1px solid var(--color__indigo--800);
    height: calc(var(--padding) * 31.25); /* 500px equivalent */
    border-radius: var(--border-radius);
    width: 100%;
  }

  .masonry {
    display: flex; flex-wrap: wrap; gap: calc(var(--padding) * 2.5); justify-content: space-around;
  }

  .masonry-row {
    display: flex;
    align-items: center;
  }

  @media only screen and (max-width: 480px) {
    body {
      width: 90%;
      margin: 0 auto;
    }

    article {
      width: 100%;
      text-align: left;
    }

    section {
      text-align: left;
    }

    .gallery__item {
      width: 100%;
      text-align: left;
      padding: calc(var(--padding) * 1.5);
      margin-bottom: calc(var(--padding) * 1.5);
    }

    .hero__buttons {
      display: flex;
    }

    .logo-section {
      justify-content: flex-start;
      /*
      padding: 2em 0 1em 0; */
      margin-top: 2em;
    }

    .logo-container {
      text-align: left;
    }

    .sensefolks-logo {
      width: 188px;
      max-width: 90vw;
    }

    .logo-footnote {
      text-align: right;
    }

    /* Mobile form styles */
    .form-group {
      flex-direction: column;
    }

    .waitlist-input {
      border-radius: calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0 0;
      border-right: 1px solid white;
      border-bottom: none;
    }

    .early-access-input {
      border-radius: calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6) 0 0;
      border-right: 1px solid white;
      border-bottom: none;
    }

    .waitlist-button {
      border-radius: 0 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6);
      border-top: none;
    }

    .early-access-button {
      border-radius: 0 0 calc(var(--border-radius) * 1.6) calc(var(--border-radius) * 1.6);
      border-top: none;
    }

    .benefits__row {
      display: block;
    }

    .masonry {
      display: block;
    }

    .masonry-row {
      align-items: baseline;
      margin-bottom: calc(var(--padding) * 2);
    }

    .dashboard__benefit .row {
      align-items: baseline;
    }

    .content--narrow {
      width: 100%;
    }

    .survey__benefits .row,
    .dashboard__benefit .row {
      display: flex;
      align-items: baseline;
    }

    .survey__preview__bg {
      width: 100%;
    }

    .survey__thumbnail__description {
      width: 100%;
    }

    .cta__container {
      padding: calc(var(--padding) * 1.5);
    }

    .material-symbols-outlined {
      z-index: 9;
    }

    .dashboard-section {
      padding: calc(var(--padding) * 2 + 2em) calc(var(--padding) * 2) calc(var(--padding) * 2) calc(var(--padding) * 2);
    }
  }
</style>

<script>
  // Email validation function
  function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Show error message
  function showError(errorElementId: string, message: string): void {
    const errorElement = document.getElementById(errorElementId);
    if (errorElement) {
      errorElement.textContent = message;
      errorElement.style.display = 'block';
    }
  }

  // Hide error message
  function hideError(errorElementId: string): void {
    const errorElement = document.getElementById(errorElementId);
    if (errorElement) {
      errorElement.style.display = 'none';
    }
  }

  // Validate form
  function validateForm(emailInputId: string, errorElementId: string): boolean {
    const emailInput = document.getElementById(emailInputId) as HTMLInputElement;
    if (!emailInput) return false;

    const email = emailInput.value.trim();

    hideError(errorElementId);

    if (!email) {
      showError(errorElementId, 'Please enter your email address');
      return false;
    }

    if (!isValidEmail(email)) {
      showError(errorElementId, 'Please enter a valid email address');
      return false;
    }

    return true;
  }

  // Show spinner in button
  function showSpinner(buttonElement: HTMLButtonElement | null, spinnerClass: string): void {
    if (buttonElement) {
      buttonElement.innerHTML = '<div class="spinner ' + spinnerClass + '"></div>';
      buttonElement.classList.add('loading');
    }
  }

  // Hide spinner and restore button text
  function hideSpinner(buttonElement: HTMLButtonElement | null, originalText: string): void {
    if (buttonElement) {
      buttonElement.innerHTML = originalText;
      buttonElement.classList.remove('loading');
    }
  }

  // Submit form to API
  async function submitToWaitlist(email: string, location: string): Promise<{success: boolean, message: string}> {
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const apiUrl = isDevelopment
      ? 'http://localhost:4444/v1/waitlist'
      : 'https://api.sensefolks.com/v1/waitlist';

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: email,
        meta: {
          source: 'homepage',
          location: location
        }
      })
    });

    return await response.json();
  }

  // Show success message and hide form
  function showSuccess(formElement: HTMLFormElement, message: string): void {
    if (formElement) {
      formElement.style.display = 'none';
      const successDiv = document.createElement('div');
      successDiv.style.color = '#81c784'; // Green text color
      successDiv.style.fontSize = 'calc(var(--font-size__body) * 0.94)';
      successDiv.style.marginTop = 'var(--padding)';
      successDiv.style.textAlign = 'center';
      successDiv.textContent = message;
      formElement.parentNode?.insertBefore(successDiv, formElement.nextSibling);
    }
  }

  // Add event listeners when DOM is loaded
  document.addEventListener('DOMContentLoaded', function() {
    // Waitlist form validation
    const waitlistForm = document.getElementById('waitlist-form') as HTMLFormElement;
    if (waitlistForm) {
      waitlistForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const submitButton = waitlistForm.querySelector('.waitlist-button') as HTMLButtonElement;
        const originalText = submitButton ? submitButton.innerHTML : 'Join Waitlist';
        const emailInput = document.getElementById('waitlist-email') as HTMLInputElement;

        if (validateForm('waitlist-email', 'waitlist-error')) {
          // Show spinner
          showSpinner(submitButton, 'waitlist-spinner');

          try {
            const result = await submitToWaitlist(emailInput.value.trim(), 'below-hero');
            hideSpinner(submitButton, originalText);

            if (result.success) {
              showSuccess(waitlistForm, 'Thanks for your interest! We will get in touch soon');
            } else {
              showError('waitlist-error', result.message || 'Something went wrong. Please try again.');
            }
          } catch (error) {
            hideSpinner(submitButton, originalText);
            showError('waitlist-error', 'Network error. Please try again.');
          }
        }
      });
    }

    // Early access form validation
    const earlyAccessForm = document.getElementById('early-access-form') as HTMLFormElement;
    if (earlyAccessForm) {
      earlyAccessForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const submitButton = earlyAccessForm.querySelector('.early-access-button') as HTMLButtonElement;
        const originalText = submitButton ? submitButton.innerHTML : 'Get Early Access';
        const emailInput = document.getElementById('early-access-email') as HTMLInputElement;

        if (validateForm('early-access-email', 'early-access-error')) {
          // Show spinner
          showSpinner(submitButton, 'early-access-spinner');

          try {
            const result = await submitToWaitlist(emailInput.value.trim(), 'below-dashboard');
            hideSpinner(submitButton, originalText);

            if (result.success) {
              showSuccess(earlyAccessForm, 'Thanks for your interest! We will get in touch soon');
            } else {
              showError('early-access-error', result.message || 'Something went wrong. Please try again.');
            }
          } catch (error) {
            hideSpinner(submitButton, originalText);
            showError('early-access-error', 'Network error. Please try again.');
          }
        }
      });
    }
  });
</script>
